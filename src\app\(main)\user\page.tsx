'use client'

import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Link as LinkIcon,
  Edit as EditIcon,
  PersonOff as PersonOffIcon,
  Person as PersonIcon,
  Google as GoogleIcon,
  LinkedIn as LinkedInIcon,
  Facebook as FacebookIcon,
  Security as SecurityIcon,
  Email as EmailIcon
} from '@mui/icons-material'

// Types
interface UserProfile {
  name: string
  username: string
  description: string
  additional_links: string[],
  profile_picture_url?: string
}

// Mock data - replace with actual Supabase queries
const mockUserProfile: UserProfile = {
  name: '<PERSON>',
  username: 'johndo<PERSON>',
  description: 'Passionate entrepreneur building the next big thing in Vietnam',
  additional_links: ['https://github.com/johndoe', 'https://twitter.com/johndoe', 'https://pronhub.com/thangdinh'],
  profile_picture_url: `https://i.pravatar.cc/150?u=a042581f4e29026024d`
}

// Types
interface LinkedAccount {
  id: string
  provider: string
  email?: string
  connected_at: string
}

interface MainSettings {
  notification_email: string
  linked_accounts: LinkedAccount[]
}

// Mock data - replace with actual Supabase queries
const mockLinkedAccounts: LinkedAccount[] = [
  { id: '1', provider: 'google', email: '<EMAIL>', connected_at: '2024-01-15' },
  { id: '2', provider: 'linkedin', email: '<EMAIL>', connected_at: '2024-02-01' }
]

export default function ProfileSettingsPage() {

  // Main Settings State
    const [mainSettings, setMainSettings] = useState<MainSettings>({
      notification_email: '<EMAIL>',
      linked_accounts: mockLinkedAccounts
    })
  
    // Mutation for saving settings
    const saveMainSettings = useMutation({
      mutationFn: async (data: MainSettings) => {
        // TODO: Replace with actual backend API call
        console.log('Saving main settings:', data)
        await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
        return data
      },
      onSuccess: () => {
        alert('Main settings saved successfully!')
      },
      onError: (error: Error) => {
        console.error('Error saving main settings:', error)
        alert('Failed to save main settings')
      }
    })
  
    // Handlers
    const handleLinkAccount = (provider: string) => {
      // Implement OAuth flow
      console.log(`Linking ${provider} account`)
      alert(`Linking ${provider} account - OAuth flow would start here`)
    }
  
    const handleUnlinkAccount = (accountId: string) => {
      setMainSettings(prev => ({
        ...prev,
        linked_accounts: prev.linked_accounts.filter(acc => acc.id !== accountId)
      }))
    }
  
    const handleDeactivateAccount = () => {
      if (confirm('Are you sure you want to deactivate your account?')) {
        console.log('Deactivating account')
        alert('Account deactivation flow would start here')
      }
    }
  
    const handleDeleteAccount = () => {
      if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {
        console.log('Deleting account')
        alert('Account deletion flow would start here')
      }
    }
  
    const getProviderIcon = (provider: string) => {
      switch (provider) {
        case 'google':
          return <GoogleIcon className="text-white" />
        case 'linkedin':
          return <LinkedInIcon className="text-white" />
        case 'facebook':
          return <FacebookIcon className="text-white" />
        default:
          return <LinkIcon className="text-white" />
      }
    }
  
    const getProviderColorClass = (provider: string) => {
      switch (provider) {
        case 'google':
          return 'provider-google'
        case 'linkedin':
          return 'provider-linkedin'
        case 'facebook':
          return 'provider-facebook'
        case 'twitter':
          return 'provider-twitter'
        default:
          return 'provider-default'
      }
    }

  // Profile Settings State
  const [profileSettings, setProfileSettings] = useState<UserProfile>(mockUserProfile)
  const [newLink, setNewLink] = useState('')
  const [profileImage, setProfileImage] = useState<string | null>(mockUserProfile.profile_picture_url || null);
  const [imageFile, setImageFile] = useState<File | null>(null);


  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setProfileImage(null);
    setImageFile(null);
    setProfileSettings(prev => ({...prev, profile_picture_url: undefined}));
  };

  // Mutation for saving profile settings
  const saveProfileSettings = useMutation({
    mutationFn: async (data: UserProfile) => {
      // TODO: Replace with actual backend API call
      console.log('Saving profile settings:', data)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      return data
    },
    onSuccess: () => {
      alert('Profile settings saved successfully!')
    },
    onError: (error: Error) => {
      console.error('Error saving profile settings:', error)
      alert('Failed to save profile settings')
    }
  })

  // Handlers
  const handleAddLink = () => {
    if (newLink.trim()) {
      setProfileSettings(prev => ({
        ...prev,
        additional_links: [...prev.additional_links, newLink.trim()]
      }))
      setNewLink('')
    }
  }

  const handleRemoveLink = (index: number) => {
    setProfileSettings(prev => ({
      ...prev,
      additional_links: prev.additional_links.filter((_, i) => i !== index)
    }))
  }

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleAddLink()
    }
  }

  const getLinkDomain = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return url
    }
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Add common NavBar component here */}
        
        <div className="space-y-8">
          {/* Profile Picture Section */}
          <div className="glass-card glass-card-hover overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-12 h-12 provider-default rounded-2xl flex items-center justify-center shadow-lg">
                  <PersonIcon className="text-white text-xl" />
                </div>
                <h2 className="text-2xl font-bold text-purple-700">
                  Profile Picture
                </h2>
              </div>
              
              <div className="flex items-center gap-8">
                <div className="relative">
                  <img
                    src={profileImage || `https://i.pravatar.cc/150?u=a042581f4e29026024d`} // Fallback to a default avatar
                    alt="Profile"
                    className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  <label
                    htmlFor="profile-picture-upload"
                    className="absolute bottom-0 right-0 bg-purple-600 text-white rounded-full p-2 cursor-pointer hover:bg-purple-700 transition-all duration-200"
                  >
                    <EditIcon fontSize="small" />
                  </label>
                  <input
                    id="profile-picture-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </div>
                <div className="flex flex-col gap-4">
                  <button
                    onClick={() => document.getElementById('profile-picture-upload')?.click()}
                    className="px-6 py-3 provider-twitter text-white rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-200 font-medium flex items-center gap-2"
                  >
                    <AddIcon />
                    Change Picture
                  </button>
                  {profileImage && (
                    <button
                      onClick={handleRemoveImage}
                      className="px-6 py-3 bg-pink-500 text-white rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-200 font-medium flex items-center gap-2"
                    >
                      <DeleteIcon />
                      Remove
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Basic Information Section */}
          <div className="glass-card glass-card-hover overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-12 h-12 provider-default rounded-2xl flex items-center justify-center shadow-lg">
                  <EditIcon className="text-white text-xl" />
                </div>
                <h2 className="text-2xl font-bold text-purple-700">
                  Basic Information
                </h2>
              </div>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-purple-700 font-medium mb-3 text-sm">
                    Name
                  </label>
                  <input
                    type="text"
                    value={profileSettings.name}
                    onChange={(e) => setProfileSettings(prev => ({ ...prev, name: e.target.value }))}
                    className="input-field"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label className="block text-purple-700 font-medium mb-3 text-sm">
                    Username
                  </label>
                  <div className="relative">
                    <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-600 font-medium">@</span>
                    <input
                      type="text"
                      value={profileSettings.username}
                      onChange={(e) => setProfileSettings(prev => ({ ...prev, username: e.target.value }))}
                      className="input-field pl-8"
                      placeholder="Enter your username"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-purple-700 font-medium mb-3 text-sm">
                    Description
                  </label>
                  <textarea
                    value={profileSettings.description}
                    onChange={(e) => setProfileSettings(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className="input-field resize-none"
                    placeholder="Tell others about yourself and your interests"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Additional Links Section */}
          <div className="overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-12 h-12 provider-google rounded-2xl flex items-center justify-center shadow-lg">
                  <LinkIcon className="text-white text-xl" />
                </div>
                <h2 className="text-2xl font-bold text-purple-700">
                  Additional Links
                </h2>
              </div>
              
              {/* Existing Links */}
              {profileSettings.additional_links.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-indigo-700 mb-4">
                    Your Links
                  </h3>
                  <div className="space-y-3">
                    {profileSettings.additional_links.map((link, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 rounded-2xl border border-violet-100 hover:from-violet-100/50 hover:to-cyan-100/50 hover:translate-x-1 transition-all duration-200"
                      >
                        <div className="flex items-center gap-4 flex-1 min-w-0">
                          <div className="w-10 h-10 rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
                            <LinkIcon className="text-black text-sm" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-semibold text-indigo-700 text-sm">
                              {getLinkDomain(link)}
                            </p>
                            <p className="text-purple-600 text-xs opacity-70 truncate">
                              {link}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleRemoveLink(index)}
                          className="p-2 text-pink-500 hover:text-pink-700 hover:bg-pink-50 rounded-xl transition-all duration-200 flex-shrink-0"
                        >
                          <DeleteIcon fontSize="small" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Add New Link */}
              <div>
                <h3 className="text-lg font-semibold text-indigo-700 mb-4">
                  Add New Link
                </h3>
                <div className="flex gap-3">
                  <div className="flex-1 relative">
                    <LinkIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-600" />
                    <input
                      type="url"
                      value={newLink}
                      onChange={(e) => setNewLink(e.target.value)}
                      onKeyPress={handleKeyPress}
                      className="input-field pl-12"
                      placeholder="https://example.com"
                    />
                  </div>
                  <button
                    onClick={handleAddLink}
                    disabled={!newLink.trim()}
                    className="px-6 py-3 provider-twitter text-white rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-200 font-medium disabled:opacity-50 disabled:hover:translate-y-0 disabled:hover:shadow-lg flex items-center gap-2"
                  >
                    <AddIcon />
                  </button>
                </div>
              </div>
            </div>
          </div>
              <div className="overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 provider-default rounded-2xl flex items-center justify-center shadow-lg">
                  <LinkIcon className="text-white text-xl" />
                </div>
                <h2 className="text-2xl font-bold">
                  Linked Account Services
                </h2>
              </div>
              
              <div className="space-y-4 mb-8">
                {mainSettings.linked_accounts.map((account) => (
                  <div 
                    key={account.id} 
                    className="flex items-center justify-between p-4 rounded-2xl border border-violet-100">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 ${getProviderColorClass(account.provider)} rounded-2xl flex items-center justify-center shadow-lg`}>
                        {getProviderIcon(account.provider)}
                      </div>
                      <div>
                        <p className="font-semibold text-indigo-700 capitalize text-lg">
                          {account.provider}
                        </p>
                        {account.email && (
                          <p className="text-purple-600 text-sm opacity-80">
                            {account.email}
                          </p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => handleUnlinkAccount(account.id)}
                      className="px-4 py-2 border-2 border-pink-300 text-pink-600 rounded-xl"
                    >
                      Unlink
                    </button>
                  </div>
                ))}
              </div>

              <div className="border-t border-violet-200 pt-6">
                <h3 className="text-lg font-semibold text-purple-700 mb-4">
                  Link More Accounts
                </h3>
                <div className="flex gap-4 flex-wrap">
                  <button
                    onClick={() => handleLinkAccount('facebook')}
                    className="flex items-center gap-3 px-6 py-3 border-2 border-indigo-300 text-indigo-600 rounded-xl"
                  >
                    <FacebookIcon />
                    Facebook
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Email Section */}
          <div className="bg-white/80 backdrop-blur-lg rounded-3xl border border-violet-100 shadow-xl shadow-violet-500/10 overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg">
                  <EmailIcon className="text-black text-xl" />
                </div>
                <h2 className="text-2xl font-bold text-purple-700">
                  Notification Email
                </h2>
              </div>
              
              <div className="max-w-md">
                <label className="block text-purple-700 font-medium mb-3 text-sm">
                  Email for notifications
                </label>
                <input
                  type="email"
                  value={mainSettings.notification_email}
                  onChange={(e) => setMainSettings(prev => ({ ...prev, notification_email: e.target.value }))}
                  className="input-field"
                  placeholder="Enter email address"
                />
              </div>
            </div>
          </div>

          {/* Account Actions Section */}
          <div className="bg-white/80 backdrop-blur-lg rounded-3xl border border-violet-100 shadow-xl shadow-violet-500/10 overflow-hidden">
            <div className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg">
                  <SecurityIcon className="text-black text-xl" />
                </div>
                <h2 className="text-2xl font-bold text-purple-700">
                  Account Actions
                </h2>
              </div>
              
              <div className="space-y-6">
                <div className="p-6 bg-amber-50 rounded-2xl border-2 border-amber-200">
                  <button
                    onClick={handleDeactivateAccount}
                    className="flex items-center gap-3 px-6 py-3 border-2 border-amber-400 text-amber-700 rounded-xl"
                  >
                    <PersonOffIcon />
                    Deactivate Account
                  </button>
                  <p className="text-amber-700 text-sm">
                    Temporarily disable your account. You can reactivate it later.
                  </p>
                </div>
                
                <div className="p-6 bg-red-50 rounded-2xl border-2 border-red-200">
                  <button
                    onClick={handleDeleteAccount}
                    className="flex items-center gap-3 px-6 py-3 border-2 border-red-400 text-red-700 rounded-xl hover:border-red-600 hover:bg-red-100 transition-all duration-200 font-medium mb-3"
                  >
                    <DeleteIcon />
                    Delete Account
                  </button>
                  <p className="text-red-700 text-sm">
                    Permanently delete your account and all associated data. This cannot be undone.
                  </p>
                </div>
              </div>
            </div>
          </div>
          


          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={() => saveProfileSettings.mutate(profileSettings)}
              disabled={saveProfileSettings.isPending}
              className="gradient-button duration-200 font-semibold text-lg disabled:opacity-50 disabled:hover:translate-y-0 disabled:hover:shadow-xl">
              {saveProfileSettings.isPending ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}