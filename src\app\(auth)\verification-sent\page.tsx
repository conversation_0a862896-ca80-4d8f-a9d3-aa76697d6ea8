'use client';

import { Box, Button, Paper, Typography } from '@mui/material';
import { MailCheck } from 'lucide-react';
import Link from 'next/link';

export default function VerificationSent() {
  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
          <MailCheck size={48} color="#1976d2" />
        </Box>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          Check your email
        </Typography>
        <Typography color="text.secondary" align="center" mb={3}>
          We&apos;ve sent a verification link to your email address. Please check your inbox and click the link to verify your account.
        </Typography>
        <Typography color="text.secondary" align="center" mb={4} fontSize={14}>
          Didn&apos;t receive the email? Check your spam folder or{' '}
          <Link href="/sign-up" style={{ color: '#1976d2', fontWeight: 500 }}>
            try again
          </Link>
        </Typography>
      </Paper>
    </Box>
  )
}
