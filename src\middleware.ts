import { type NextRequest } from 'next/server'
import { updateSession } from '@/config/supabase/supabase-middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - *.png, *.jpg, *.jpeg, *.gif, *.svg (image files)
     * - sign-in, sign-up, auth (authentication routes)
     * - api/auth (authentication API routes)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:png|jpg|jpeg|gif|svg)$|sign-in|sign-up|auth|api/auth).*)',
  ],
}