'use client';
import { But<PERSON>, <PERSON>Field, Typography, Paper, Divider, Box } from '@mui/material';
import { signInWithGoogle, signUpWithEmail } from '@/lib/auth';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { ChromeIcon } from 'lucide-react';
import { appName } from '@/config/constant';
import { useRouter } from 'next/navigation';

export default function SignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const router = useRouter();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (password === confirmPassword) {
      await signUpWithEmail(email, password);
      router.push('/verification-sent');
    } else {
      alert('Passwords do not match');
    }
  };

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Create your account
        </Typography>
        <form onSubmit={handleSignUp}>
          <TextField required label="Email Address" variant="outlined" fullWidth margin="normal" value={email} onChange={e => setEmail(e.target.value)} />
          <TextField required label="Password" type="password" variant="outlined" fullWidth margin="normal" value={password} onChange={e => setPassword(e.target.value)} />
          <TextField required label="Confirm Password" type="password" variant="outlined" fullWidth margin="normal" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} />
          <Button type="submit" variant="contained" color="primary" fullWidth sx={{ mt: 2 }}>
            Continue
          </Button>
        </form>
        <Divider sx={{ my: 3 }}>or</Divider>
        <Button
          variant="outlined"
          color="primary"
          fullWidth
          startIcon={<ChromeIcon className="w-5 h-5 mr-2" />}
          onClick={signInWithGoogle}
        >
          Sign up with Google
        </Button>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          Already have an account?{' '}
          <Link href="/sign-in" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign in
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}

