'use client';
import { <PERSON><PERSON>, <PERSON>Field, Typography, Paper, Divider, Box, Alert } from '@mui/material';
import { signInWithGoogle, signUpWithEmail } from '@/lib/auth';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { ChromeIcon } from 'lucide-react';
import { appName } from '@/config/constant';
import { useRouter } from 'next/navigation';

export default function SignUp() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Client-side validation
    if (password !== confirmPassword) {
      setError('Passwords do not match. Please make sure both password fields are identical.');
      setIsLoading(false);
      return;
    }

    try {
      await signUpWithEmail(email, password);
      router.push('/verification-sent');
    } catch (error: any) {
      console.error('Sign-up error:', error);

      // Handle specific Supabase error messages
      const errorMessage = error?.message || 'An unexpected error occurred';

      if (errorMessage.includes('User already registered')) {
        setError('This email is already registered. Please sign in instead or use a different email address.');
      } else if (errorMessage.includes('Password should be at least')) {
        setError('Password must be at least 6 characters long.');
      } else if (errorMessage.includes('Unable to validate email address') || errorMessage.includes('invalid format')) {
        setError('Please enter a valid email address.');
      } else if (errorMessage.includes('Password must contain')) {
        setError('Password must contain at least one uppercase letter, one lowercase letter, and one number.');
      } else if (errorMessage.includes('Too many requests')) {
        setError('Too many sign-up attempts. Please wait a moment before trying again.');
      } else if (errorMessage.includes('Failed to fetch') || errorMessage.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Create your account
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSignUp}>
          <TextField required label="Email Address" variant="outlined" fullWidth margin="normal" value={email} onChange={e => setEmail(e.target.value)} />
          <TextField required label="Password" type="password" variant="outlined" fullWidth margin="normal" value={password} onChange={e => setPassword(e.target.value)} />
          <TextField required label="Confirm Password" type="password" variant="outlined" fullWidth margin="normal" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
            disabled={isLoading}
          >
            {isLoading ? 'Creating account...' : 'Continue'}
          </Button>
        </form>
        <Divider sx={{ my: 3 }}>or</Divider>
        <Button
          variant="outlined"
          color="primary"
          fullWidth
          startIcon={<ChromeIcon className="w-5 h-5 mr-2" />}
          onClick={async () => {
            try {
              setError('');
              await signInWithGoogle();
            } catch (error: any) {
              console.error('Google sign-up error:', error);
              setError(error?.message || 'Failed to sign up with Google. Please try again.');
            }
          }}
          disabled={isLoading}
        >
          Sign up with Google
        </Button>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          Already have an account?{' '}
          <Link href="/sign-in" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign in
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}

