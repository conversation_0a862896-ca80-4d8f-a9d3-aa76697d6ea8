import { baseUrl } from "@/config/constant";
import { createClient } from "@/config/supabase/supabase-client";

const signInWithGoogle = async () => {
  const supabase = createClient()
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${baseUrl}/auth/callback`,
    },
  });

  if (error) {
    // Handle specific Google OAuth errors
    if (error.message.includes('popup_closed_by_user')) {
      throw new Error('Sign-in was cancelled. Please try again.');
    } else if (error.message.includes('access_denied')) {
      throw new Error('Access denied. Please allow permissions to continue.');
    } else if (error.message.includes('popup_blocked')) {
      throw new Error('Popup was blocked. Please allow popups for this site and try again.');
    }
    throw error;
  }

  return data;
};

const signUpWithEmail = async (email: string, password: string) => {
  const supabase = createClient()
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${baseUrl}/auth/callback`,
      data: {
        email: email
      }
    },
  });

  if (error) {
    throw error;
  }

  return data;
};

const signInWithEmail = async (email: string, password: string) => {
  const supabase = createClient()
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    throw error;
  }
};

const resetPassword = async (email: string) => {
  const supabase = createClient()
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: baseUrl,
  });

  if (error) {
    throw error;
  }
};

const getSession = async () => {
  const supabase = createClient()
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    throw error;
  }

  return session;
};

const signOut = async () => {
  const supabase = createClient()
  const { error } = await supabase.auth.signOut();

  if (error) {
    throw error;
  }
};

export {
  signInWithGoogle,
  signUpWithEmail,
  signInWithEmail,
  resetPassword,
  getSession,
  signOut
};
