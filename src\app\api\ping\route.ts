import { NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json({ message: "pong" });
}

export async function POST(request: Request) {
  const body = await request.json();
  console.log(body);
  return NextResponse.json({ message: "pong" });
}

export async function PUT(request: Request) {
  const body = await request.json();
  console.log(body);
  return NextResponse.json({ message: "pong" });
}