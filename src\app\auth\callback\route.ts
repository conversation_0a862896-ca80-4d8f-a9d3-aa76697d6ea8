import { NextResponse } from 'next/server'
// The client you created from the Server-Side Auth instructions
import { createClient } from '@/config/supabase/supabase-server'
import { prisma } from '@/config/prisma-client'

export async function GET(request: Request) {
    const { searchParams, origin } = new URL(request.url)
    const code = searchParams.get('code')
    // if "next" is in param, use it as the redirect URL
    let next = searchParams.get('next') ?? '/'
    if (!next.startsWith('/')) {
        // if "next" is not a relative URL, use the default
        next = '/'
    }

    if (code) {
        const supabase = await createClient()
        const { data, error } = await supabase.auth.exchangeCodeForSession(code)
        if (!error && data.user) {
            // Create or update user in our database
            try {
                const existingUser = await prisma.user.findUnique({
                    where: { id: data.user.id }
                })

                if (!existingUser) {
                    // Create new user record (works for both Google OAuth and email verification)
                    const userName = data.user.user_metadata?.full_name ||
                                   data.user.user_metadata?.name ||
                                   data.user.email ||
                                   'Unknown User'

                    await prisma.user.create({
                        data: {
                            id: data.user.id,
                            userName: userName,
                            createdAt: new Date(),
                        }
                    })
                    console.log('Created new user:', {
                        id: data.user.id,
                        email: data.user.email,
                        userName: userName,
                        provider: data.user.app_metadata?.provider || 'email'
                    })
                }
            } catch (dbError) {
                console.error('Error creating user in database:', dbError)
                // Continue with redirect even if database operation fails
            }

            const forwardedHost = request.headers.get('x-forwarded-host') // original origin before load balancer
            const isLocalEnv = process.env.NODE_ENV === 'development'
            if (isLocalEnv) {
                // we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
                return NextResponse.redirect(`${origin}${next}`)
            } else if (forwardedHost) {
                return NextResponse.redirect(`https://${forwardedHost}${next}`)
            } else {
                return NextResponse.redirect(`${origin}${next}`)
            }
        }
    }

    // return the user to an error page with instructions
    return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}