
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON>r, TextField, Paper, Box } from "@mui/material";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div
      className={`min-h-screen w-full flex items-center justify-center bg-gradient-to-r from-purple-700 via-blue-500 to-cyan-300 antialiased`}
    >
      {children}
    </div>
  );
}
