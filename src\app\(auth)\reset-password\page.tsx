'use client';

import { <PERSON>, But<PERSON>, Paper, TextField, Typography } from '@mui/material';
import { appName } from '@/config/constant';
import { useState } from 'react';
import Link from 'next/link';
import { resetPassword } from '@/lib/auth';

export default function ResetPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await resetPassword(email);

      setIsSubmitted(true);
    } catch (error) {
      console.error('Error resetting password:', error);
    }
  };

  if (isSubmitted) {
    return (
      <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
        <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
          <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
            Check your email
          </Typography>
          <Typography color="text.secondary" align="center" mb={3}>
            We&apos;ve sent password reset instructions to your email address. Please check your inbox.
          </Typography>
          <Button
            component={Link}
            href="/sign-in"
            variant="outlined"
            color="primary"
            fullWidth
          >
            Back to Sign In
          </Button>
        </Paper>
      </Box>
    );
  }

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Reset your password
        </Typography>
        <form onSubmit={handleResetPassword}>
          <TextField
            required
            label="Email"
            variant="outlined"
            fullWidth
            margin="normal"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
          >
            Send Reset Instructions
          </Button>
        </form>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          Remember your password?{' '}
          <Link href="/sign-in" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign in
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}
