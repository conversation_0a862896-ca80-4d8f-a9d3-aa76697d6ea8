'use client';

import { <PERSON>, <PERSON><PERSON>, Paper, <PERSON><PERSON>ield, <PERSON><PERSON><PERSON>, Alert } from '@mui/material';
import { appName } from '@/config/constant';
import { useState } from 'react';
import Link from 'next/link';
import { resetPassword } from '@/lib/auth';

export default function ResetPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await resetPassword(email);
      setIsSubmitted(true);
    } catch (error: any) {
      console.error('Error resetting password:', error);

      // Handle specific Supabase error messages
      const errorMessage = error?.message || 'An unexpected error occurred';

      if (errorMessage.includes('User not found')) {
        setError('No account found with this email address. Please check your email or sign up for a new account.');
      } else if (errorMessage.includes('Unable to validate email address')) {
        setError('Please enter a valid email address.');
      } else if (errorMessage.includes('Too many requests')) {
        setError('Too many reset attempts. Please wait a moment before trying again.');
      } else if (errorMessage.includes('Failed to fetch') || errorMessage.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
        <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
          <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
            Check your email
          </Typography>
          <Typography color="text.secondary" align="center" mb={3}>
            We&apos;ve sent password reset instructions to your email address. Please check your inbox.
          </Typography>
          <Button
            component={Link}
            href="/sign-in"
            variant="outlined"
            color="primary"
            fullWidth
          >
            Back to Sign In
          </Button>
        </Paper>
      </Box>
    );
  }

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Reset your password
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleResetPassword}>
          <TextField
            required
            label="Email"
            variant="outlined"
            fullWidth
            margin="normal"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
            disabled={isLoading}
          >
            {isLoading ? 'Sending...' : 'Send Reset Instructions'}
          </Button>
        </form>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          Remember your password?{' '}
          <Link href="/sign-in" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign in
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}
