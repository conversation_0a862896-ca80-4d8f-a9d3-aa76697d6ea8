"use client";

import { appName } from "@/config/constant";
import { signInWithGoogle, signInWithEmail } from "@/lib/auth";
import { Box, Button, Divider, Paper, TextField, Typography, Alert } from "@mui/material";
import { ChromeIcon } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await signInWithEmail(email, password);
      // Small delay to ensure session is established
      await new Promise(resolve => setTimeout(resolve, 100));
      // Redirect to main page after successful sign-in
      router.push('/');
    } catch (error: any) {
      console.error('Sign-in error:', error);

      // Handle specific Supabase error messages
      const errorMessage = error?.message || 'An unexpected error occurred';

      if (errorMessage.includes('Invalid login credentials')) {
        setError('Invalid email or password. Please check your credentials and try again.');
      } else if (errorMessage.includes('Email not confirmed')) {
        setError('Please check your email and click the confirmation link before signing in.');
      } else if (errorMessage.includes('Too many requests')) {
        setError('Too many sign-in attempts. Please wait a moment before trying again.');
      } else if (errorMessage.includes('Unable to validate email address')) {
        setError('Please enter a valid email address.');
      } else if (errorMessage.includes('Failed to fetch') || errorMessage.includes('Network')) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box minHeight="100vh" display="flex" alignItems="center" justifyContent="center">
      <Paper elevation={6} sx={{ p: 4, width: '100%', maxWidth: 400, borderRadius: 3 }}>
        <Typography variant="h4" fontWeight="bold" align="center" gutterBottom>
          {appName}
        </Typography>
        <Typography color="text.secondary" align="center" mb={2}>
          Sign in to your account
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSignIn}>
          <TextField required label="Email" variant="outlined" fullWidth margin="normal" value={email} onChange={e => setEmail(e.target.value)} />
          <TextField 
            required 
            label="Password" 
            type="password" 
            variant="outlined" 
            fullWidth 
            margin="normal" 
            value={password} 
            onChange={e => setPassword(e.target.value)} 
          />
          <Box display="flex" justifyContent="flex-end" mt={1} mb={2}>
            <Link href="/reset-password" style={{ color: '#1976d2', fontSize: '14px', textDecoration: 'none' }}>
              Forgot password?
            </Link>
          </Box>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Continue'}
          </Button>
        </form>
        <Divider sx={{ my: 3 }}>or</Divider>
        <Button
          variant="outlined"
          color="primary"
          fullWidth
          startIcon={<ChromeIcon className="w-5 h-5 mr-2" />}
          onClick={async () => {
            try {
              setError('');
              await signInWithGoogle();
            } catch (error: any) {
              console.error('Google sign-in error:', error);
              setError(error?.message || 'Failed to sign in with Google. Please try again.');
            }
          }}
          disabled={isLoading}
        >
          Sign in with Google
        </Button>
        <Typography align="center" mt={3} color="text.secondary" fontSize={14}>
          No account?{' '}
          <Link href="/sign-up" style={{ color: '#1976d2', fontWeight: 500 }}>
            Sign up
          </Link>
        </Typography>
      </Paper>
    </Box>
  );
}

