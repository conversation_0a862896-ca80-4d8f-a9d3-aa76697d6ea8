import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist_Mono } from "next/font/google";

import "@/styles/globals.css";
import Providers from "./providers";

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Nexus",
  description: "Where startup spawn",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`
          ${geistMono.variable} 
          antialiased h-screen w-screen
          flex flex-col
          font-mono
        `}
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
