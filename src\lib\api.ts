interface ApiRequestOptions extends RequestInit {
  requiresAuth?: boolean;
}

export async function apiRequest<T>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<T> {
  const { requiresAuth = true, headers = {}, ...rest } = options;

  // Get the current session if authentication is required
  // let authHeader = {};
  // if (requiresAuth) {
  //   const { data: { session } } = await supabase.auth.getSession();
  //   if (!session) {
  //     throw new Error('No active session found');
  //   }
  //   authHeader = {
  //     'Authorization': `Bearer ${session.access_token}`
  //   };
  // }

  const response = await fetch(endpoint, {
    ...rest,
    headers: {
      'Content-Type': 'application/json',
      // ...authHeader,
      ...headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => null);
    throw new Error(errorData?.detail || `API request failed: ${response.statusText}`);
  }

  // Return null for 204 No Content responses
  if (response.status === 204) {
    return null as T;
  }

  return response.json();
}

export async function uploadFormData<T>(endpoint: string, formData: FormData): Promise<T> {
  // const { data: { session } } = await supabase.auth.getSession();
  // if (!session) {
  //   throw new Error('No active session found');
  // }

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      // 'Authorization': `Bearer ${session.access_token}`
      // Don't set Content-Type - browser will set it automatically with boundary for FormData
    },
    body: formData
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => null);
    throw new Error(errorData?.detail || `Upload failed: ${response.statusText}`);
  }

  return response.json();
}

// Example usage:
// GET request
export const getData = <T>(endpoint: string, options?: ApiRequestOptions) => 
  apiRequest<T>(endpoint, { method: 'GET', ...options });

// POST request
export const postData = <T, D = unknown>(endpoint: string, data: D, options?: ApiRequestOptions) => 
  apiRequest<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });

// PUT request
export const patchData = <T, D = unknown>(endpoint: string, data: D, options?: ApiRequestOptions) => 
  apiRequest<T>(endpoint, {
    method: 'PATCH',
    body: JSON.stringify(data),
    ...options,
  });

// DELETE request
export const deleteData = <T>(endpoint: string, options?: ApiRequestOptions) => 
  apiRequest<T>(endpoint, { method: 'DELETE', ...options }); 