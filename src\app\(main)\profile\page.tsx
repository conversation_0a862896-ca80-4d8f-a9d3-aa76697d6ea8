'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Settings as SettingsIcon,
  LocationOn as LocationIcon,
  Link as LinkIcon,
  CalendarToday as CalendarIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Inventory as InventoryIcon,
  Launch as LaunchIcon
} from '@mui/icons-material'

// Types
interface User {
  id: string
  name: string
  username: string
  bio: string
  location?: string
  website?: string
  joinedDate: string
  profilePicture: string
  coverImage?: string
  followers: number
  following: number
  isFollowing: boolean
}

interface Product {
  id: string
  name: string
  description: string
  image: string
  launchDate: string
  status: 'launched' | 'coming_soon' | 'in_development'
  url?: string
}

// Mock data
const mockUser: User = {
  id: '1',
  name: '<PERSON><PERSON> <PERSON>',
  username: 'brokeguy-coding',
  bio: 'Maker of things, lover of code. Building products that matter. Master III Wild Rift',
  location: 'San Francisco, CA',
  website: 'https://quangthang.dev',
  joinedDate: '2023-03-15',
  profilePicture: 'https://i.pravatar.cc/400?img=57',
  coverImage: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200&h=300&fit=crop',
  followers: 1247,
  following: 342,
  isFollowing: false
}

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'TeamSync',
    description: 'Real-time collaboration platform for distributed teams',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop',
    launchDate: '2024-01-15',
    status: 'launched',
    url: 'https://teamsync.app'
  },
  {
    id: '2',
    name: 'CodeReview AI',
    description: 'AI-powered code review assistant for faster development',
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop',
    launchDate: '2024-04-20',
    status: 'coming_soon'
  },
  {
    id: '3',
    name: 'DesignSystem Pro',
    description: 'Complete design system toolkit for modern web apps',
    image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop',
    launchDate: '2024-06-01',
    status: 'in_development'
  }
]

export default function ProfilePage() {
  const [isFollowing, setIsFollowing] = useState(mockUser.isFollowing)
  const [followerCount, setFollowerCount] = useState(mockUser.followers)

  const handleFollowToggle = () => {
    setIsFollowing(!isFollowing)
    setFollowerCount(prev => isFollowing ? prev - 1 : prev + 1)
  }

  const getStatusColor = (status: Product['status']) => {
    switch (status) {
      case 'launched':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'coming_soon':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'in_development':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: Product['status']) => {
    switch (status) {
      case 'launched':
        return 'Live'
      case 'coming_soon':
        return 'Coming Soon'
      case 'in_development':
        return 'In Development'
      default:
        return 'Unknown'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-cyan-50 to-violet-100">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Cover Image */}
        <div className="relative mb-8">
          <div 
            className="h-48 rounded-3xl bg-gradient-to-r from-purple-500 to-cyan-500 bg-cover bg-center"
            style={mockUser.coverImage ? { backgroundImage: `url(${mockUser.coverImage})` } : {}}
          />
          
          {/* Profile Picture */}
          <div className="absolute -bottom-16 left-8">
            <img
              src={mockUser.profilePicture}
              alt={mockUser.name}
              className="w-32 h-32 rounded-full border-4 border-white shadow-xl"
            />
          </div>

          {/* Settings Button */}
          <Link 
            href="/user"
            className="absolute top-4 right-4 p-3 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-200"
          >
            <SettingsIcon className="text-purple-700" />
          </Link>
        </div>

        {/* Profile Info */}
        <div className="pt-20 pb-8">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-purple-800 mb-2">
                {mockUser.name}
              </h1>
              <p className="text-purple-600 text-lg mb-4">
                @{mockUser.username}
              </p>
              
              <p className="text-gray-700 text-lg leading-relaxed mb-6 max-w-2xl">
                {mockUser.bio}
              </p>

              {/* Profile Details */}
              <div className="flex flex-wrap gap-6 text-sm text-purple-600 mb-6">
                {mockUser.location && (
                  <div className="flex items-center gap-2">
                    <LocationIcon fontSize="small" />
                    <span>{mockUser.location}</span>
                  </div>
                )}
                
                {mockUser.website && (
                  <div className="flex items-center gap-2">
                    <LinkIcon fontSize="small" />
                    <a 
                      href={mockUser.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-purple-800 hover:underline"
                    >
                      {mockUser.website.replace('https://', '')}
                    </a>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <CalendarIcon fontSize="small" />
                  <span>Joined {new Date(mockUser.joinedDate).toLocaleDateString('en-US', { 
                    month: 'long', 
                    year: 'numeric' 
                  })}</span>
                </div>
              </div>

              {/* Followers/Following */}
              <div className="flex gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <PeopleIcon fontSize="small" className="text-purple-600" />
                  <span className="font-semibold text-purple-800">{followerCount.toLocaleString()}</span>
                  <span className="text-purple-600">followers</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-purple-800">{mockUser.following.toLocaleString()}</span>
                  <span className="text-purple-600">following</span>
                </div>
              </div>
            </div>

            {/* Follow Button */}
            <div className="flex-shrink-0">
              <button
                onClick={handleFollowToggle}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl hover:-translate-y-0.5 ${
                  isFollowing 
                    ? 'bg-purple-100 text-purple-700 border-2 border-purple-200 hover:bg-purple-50' 
                    : 'bg-purple-600 text-white hover:bg-purple-700'
                }`}
              >
                <PersonAddIcon fontSize="small" />
                {isFollowing ? 'Following' : 'Follow'}
              </button>
            </div>
          </div>
        </div>

        {/* Products Section */}
        <div className="bg-white/80 backdrop-blur-lg rounded-3xl border border-violet-100 shadow-xl shadow-violet-500/10 p-8">
          <div className="flex items-center gap-4 mb-8">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
              <InventoryIcon className="text-white text-xl" />
            </div>
            <h2 className="text-2xl font-bold text-purple-700">
              Products ({mockProducts.length})
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-2xl border border-violet-100 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 overflow-hidden"
              >
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                />
                
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-purple-800">
                      {product.name}
                    </h3>
                    <span className={`px-2 py-1 rounded-lg text-xs font-medium border ${getStatusColor(product.status)}`}>
                      {getStatusText(product.status)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {product.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-purple-600">
                      {product.status === 'launched' ? 'Launched' : 'Expected'} {new Date(product.launchDate).toLocaleDateString()}
                    </span>
                    
                    {product.url && product.status === 'launched' && (
                      <a
                        href={product.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-purple-600 hover:text-purple-800 text-sm font-medium"
                      >
                        <span>Visit</span>
                        <LaunchIcon fontSize="small" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {mockProducts.length === 0 && (
            <div className="text-center py-12">
              <InventoryIcon className="text-purple-300 text-6xl mb-4" />
              <p className="text-purple-600 text-lg">No products yet</p>
              <p className="text-purple-500 text-sm">This user hasn't launched any products</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}