generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id          String     @id @default(uuid())
  createdAt   DateTime   @default(now())
  updatedAt   DateTime?
  userName    String?
  bio         String?
  websiteUrl String?
  role        String?
  company     String?
  
  accountId   String?
  
  comments    Comment[]
  posts       Post[]
  reactions   Reaction[]
}

model Post {
  id          String     @id @default(uuid())
  title       String
  description String
  body        String
  userId      String
  status      String
  thumbnailUrl String?
  categoryId  String?
  website     String?
  isPublished Boolean    @default(false)
  isPinned    Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  comments    Comment[]
  category    Category?  @relation(fields: [categoryId], references: [id])
  user        User       @relation(fields: [userId], references: [id])
  tags        PostTag[]
  reactions   Reaction[]
}

model Tag {
  id        String    @id @default(uuid())
  name      String
  color     String
  createdAt DateTime
  posts     PostTag[]
}

model PostTag {
  id     String @id @default(uuid())
  postId String
  tagId  String
  post   Post   @relation(fields: [postId], references: [id])
  tag    Tag    @relation(fields: [tagId], references: [id])
}

model Category {
  id        String   @id @default(uuid())
  name      String
  color     String
  createdAt DateTime
  updatedAt DateTime
  posts     Post[]
}

model Comment {
  id        String    @id @default(uuid())
  content   String
  postId    String
  userId    String
  parentId  String?
  createdAt DateTime
  updatedAt DateTime
  parent    Comment?  @relation("CommentToComment", fields: [parentId], references: [id])
  replies   Comment[] @relation("CommentToComment")
  post      Post      @relation(fields: [postId], references: [id])
  user      User      @relation(fields: [userId], references: [id])
}

model Reaction {
  id        String   @id @default(uuid())
  type      String
  userId    String
  postId    String
  createdAt DateTime
  post      Post     @relation(fields: [postId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}
